<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\ReviewResource;
use App\Http\Resources\Api\ReviewSummaryResource;
use App\Models\FamilyMember;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

/**
 * @group Reviews
 *
 * APIs for managing user reviews
 */
class ReviewController extends Controller
{
    /**
     * Store a new review
     *
     * @authenticated
     *
     * @response scenario=success
     * {
     *    "message": "Review created successfully",
     *    "data": {
     *        "id": 1,
     *        "rating": 5,
     *        "comment": "Great family member!",
     *        "reviewer": {
     *            "id": 1,
     *            "name": "<PERSON>",
     *            "image": "http://example.com/uploads/image.jpg",
     *            "thumb_image": "http://example.com/images/thumb/image.jpg"
     *        },
     *        "reviewed_user": {
     *            "id": 2,
     *            "name": "<PERSON>",
     *            "image": "http://example.com/uploads/image2.jpg",
     *            "thumb_image": "http://example.com/images/thumb/image2.jpg"
     *        },
     *        "status": true,
     *        "created_at": "2025-04-18 12:00:00",
     *        "created_at_formatted": "18 April 2025 12:00 PM",
     *        "updated_at": "2025-04-18 12:00:00"
     *    }
     * }
     *
     * @response 422 scenario="validation error"
     * {
     *    "message": "Validation error",
     *    "errors": {
     *        "reviewed_user_id": ["The reviewed user id field is required."],
     *        "rating": ["The rating must be between 1 and 5."]
     *    }
     * }
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'reviewed_user_id' => [
                'required',
                'exists:family_members,id',
                Rule::unique('reviews')->where(function ($query) {
                    return $query->where('reviewer_id', auth('sanctum')->id())
                        ->whereNull('deleted_at');
                }),
            ],
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ], [
            'reviewed_user_id.unique' => 'You have already reviewed this user.',
        ]);

        // Custom validation for self-review
        if ($request->reviewed_user_id == auth('sanctum')->id()) {
            $validator->after(function ($validator) {
                $validator->errors()->add('reviewed_user_id', 'You cannot review yourself.');
            });
        }

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $review = Review::create([
                'reviewer_id' => auth('sanctum')->id(),
                'reviewed_user_id' => $request->reviewed_user_id,
                'rating' => $request->rating,
                'comment' => $request->comment,
                'status' => true,
            ]);

            $review->load(['reviewer', 'reviewedUser']);

            return response()->json([
                'message' => __('common.messages.created_successfully'),
                'data' => new ReviewResource($review),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => __('common.messages.something_went_wrong'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get reviews for a specific user
     *
     * @authenticated
     *
     * @urlParam id integer required The ID of the user to get reviews for. Example: 1
     * @queryParam page integer The page number for pagination. Example: 1
     * @queryParam per_page integer Number of reviews per page (max 50). Example: 10
     *
     * @response scenario=success
     * {
     *    "data": [
     *        {
     *            "id": 1,
     *            "rating": 5,
     *            "comment": "Great family member!",
     *            "reviewer": {
     *                "id": 1,
     *                "name": "John Doe",
     *                "image": "http://example.com/uploads/image.jpg",
     *                "thumb_image": "http://example.com/images/thumb/image.jpg"
     *            },
     *            "reviewed_user": {
     *                "id": 2,
     *                "name": "Jane Doe",
     *                "image": "http://example.com/uploads/image2.jpg",
     *                "thumb_image": "http://example.com/images/thumb/image2.jpg"
     *            },
     *            "status": true,
     *            "created_at": "2025-04-18 12:00:00",
     *            "created_at_formatted": "18 April 2025 12:00 PM",
     *            "updated_at": "2025-04-18 12:00:00"
     *        }
     *    ],
     *    "meta": {
     *        "current_page": 1,
     *        "last_page": 1,
     *        "per_page": 10,
     *        "total": 1
     *    }
     * }
     */
    public function index(Request $request, $userId)
    {
        $validator = Validator::make(['user_id' => $userId] + $request->all(), [
            'user_id' => 'required|exists:family_members,id',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        $perPage = $request->get('per_page', 10);

        $reviews = Review::with(['reviewer', 'reviewedUser'])
            ->forUser($userId)
            ->active()
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return ReviewResource::collection($reviews);
    }

    /**
     * Get review summary/statistics for a specific user
     *
     * @authenticated
     *
     * @urlParam id integer required The ID of the user to get review summary for. Example: 1
     *
     * @response scenario=success
     * {
     *    "data": {
     *        "user": {
     *            "id": 1,
     *            "name": "Jane Doe",
     *            "image": "http://example.com/uploads/image.jpg",
     *            "thumb_image": "http://example.com/images/thumb/image.jpg"
     *        },
     *        "reviews_count": 5,
     *        "average_rating": 4.2,
     *        "ratings_breakdown": {
     *            "5": 2,
     *            "4": 2,
     *            "3": 1,
     *            "2": 0,
     *            "1": 0
     *        }
     *    }
     * }
     */
    public function summary($userId)
    {
        $validator = Validator::make(['user_id' => $userId], [
            'user_id' => 'required|exists:family_members,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = FamilyMember::findOrFail($userId);

        return response()->json([
            'data' => new ReviewSummaryResource($user),
        ]);
    }

    /**
     * Update an existing review
     *
     * @authenticated
     *
     * @urlParam id integer required The ID of the review to update. Example: 1
     *
     * @response scenario=success
     * {
     *    "message": "Review updated successfully",
     *    "data": {
     *        "id": 1,
     *        "rating": 4,
     *        "comment": "Updated comment",
     *        "reviewer": {
     *            "id": 1,
     *            "name": "John Doe",
     *            "image": "http://example.com/uploads/image.jpg",
     *            "thumb_image": "http://example.com/images/thumb/image.jpg"
     *        },
     *        "reviewed_user": {
     *            "id": 2,
     *            "name": "Jane Doe",
     *            "image": "http://example.com/uploads/image2.jpg",
     *            "thumb_image": "http://example.com/images/thumb/image2.jpg"
     *        },
     *        "status": true,
     *        "created_at": "2025-04-18 12:00:00",
     *        "created_at_formatted": "18 April 2025 12:00 PM",
     *        "updated_at": "2025-04-18 12:30:00"
     *    }
     * }
     */
    public function update(Request $request, $reviewId)
    {
        $validator = Validator::make(['review_id' => $reviewId] + $request->all(), [
            'review_id' => 'required|exists:reviews,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        $review = Review::findOrFail($reviewId);

        // Check if the authenticated user is the reviewer
        if ($review->reviewer_id !== auth('sanctum')->id()) {
            return response()->json([
                'message' => __('common.messages.unauthorized'),
            ], 403);
        }

        try {
            $review->update([
                'rating' => $request->rating,
                'comment' => $request->comment,
            ]);

            $review->load(['reviewer', 'reviewedUser']);

            return response()->json([
                'message' => __('common.messages.updated_successfully'),
                'data' => new ReviewResource($review),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => __('common.messages.something_went_wrong'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a review
     *
     * @authenticated
     *
     * @urlParam id integer required The ID of the review to delete. Example: 1
     *
     * @response scenario=success
     * {
     *    "message": "Review deleted successfully"
     * }
     */
    public function destroy($reviewId)
    {
        $validator = Validator::make(['review_id' => $reviewId], [
            'review_id' => 'required|exists:reviews,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        $review = Review::findOrFail($reviewId);

        // Check if the authenticated user is the reviewer
        if ($review->reviewer_id !== auth('sanctum')->id()) {
            return response()->json([
                'message' => __('common.messages.unauthorized'),
            ], 403);
        }

        try {
            $review->delete();

            return response()->json([
                'message' => __('common.messages.deleted_successfully'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => __('common.messages.something_went_wrong'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
