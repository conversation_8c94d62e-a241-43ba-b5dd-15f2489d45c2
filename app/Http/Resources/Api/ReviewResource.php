<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'rating' => $this->rating,
            'comment' => $this->comment,
            'reviewer' => [
                'id' => $this->reviewer->id,
                'name' => $this->reviewer->display_name,
                'image' => $this->reviewer->image_url,
                'thumb_image' => $this->reviewer->thumb_image_url,
            ],
            'reviewed_user' => [
                'id' => $this->reviewedUser->id,
                'name' => $this->reviewedUser->display_name,
                'image' => $this->reviewedUser->image_url,
                'thumb_image' => $this->reviewedUser->thumb_image_url,
            ],
            'status' => $this->status,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'created_at_formatted' => $this->created_at->translatedFormat('j F Y h:i A'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
