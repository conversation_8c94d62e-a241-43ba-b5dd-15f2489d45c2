<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReviewSummaryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $reviewStats = $this->review_stats;
        
        return [
            'user' => [
                'id' => $this->id,
                'name' => $this->display_name,
                'image' => $this->image_url,
                'thumb_image' => $this->thumb_image_url,
            ],
            'reviews_count' => $reviewStats['count'],
            'average_rating' => $reviewStats['average'],
            'ratings_breakdown' => $reviewStats['ratings_breakdown'],
        ];
    }
}
