<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Review extends Model
{
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reviewer_id',
        'reviewed_user_id',
        'rating',
        'comment',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rating' => 'integer',
        'status' => 'boolean',
    ];

    /**
     * Validation rules for the model.
     *
     * @return array<string, string>
     */
    public static function validationRules(): array
    {
        return [
            'reviewer_id' => 'required|exists:family_members,id',
            'reviewed_user_id' => 'required|exists:family_members,id|different:reviewer_id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
            'status' => 'boolean',
        ];
    }

    /**
     * Get the reviewer (family member who wrote the review).
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(FamilyMember::class, 'reviewer_id');
    }

    /**
     * Get the reviewed user (family member being reviewed).
     */
    public function reviewedUser(): BelongsTo
    {
        return $this->belongsTo(FamilyMember::class, 'reviewed_user_id');
    }

    /**
     * Scope a query to only include active reviews.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to only include reviews for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('reviewed_user_id', $userId);
    }

    /**
     * Scope a query to only include reviews by a specific reviewer.
     */
    public function scopeByReviewer($query, $reviewerId)
    {
        return $query->where('reviewer_id', $reviewerId);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Ensure a user cannot review themselves
        static::creating(function ($review) {
            if ($review->reviewer_id === $review->reviewed_user_id) {
                throw new \InvalidArgumentException('A user cannot review themselves.');
            }
        });

        static::updating(function ($review) {
            if ($review->reviewer_id === $review->reviewed_user_id) {
                throw new \InvalidArgumentException('A user cannot review themselves.');
            }
        });
    }
}
