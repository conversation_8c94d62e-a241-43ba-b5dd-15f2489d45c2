<?php

namespace App\Models;

use App\Facades\Whatsapp;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Sanctum\HasApiTokens;

class FamilyMember extends Authenticatable
{
    use HasFactory;
    use SoftDeletes;
    use HasApiTokens;

    protected $fillable = [
        'first_name',
        'middle_name',
        'last_name',
        'grandfather_name',
        'gender',
        'email',
        'mobile',
        'image',
        'branch_id',
        'password',
        'status',
        'reset_password_code',
        'birth_date',
        'birth_place',
        'country_id',
        'city_id',
        'address',
        'overview',
        'cover_image',
        'facebook_link',
        'x_link',
        'snapshot_link',
        'youtube_link',
        'linkedin_link',
        'instagram_link',
        'cv_file',
        'cv_type',
        'cv_text',
        'country_code',
        'family_tree_node_id',
    ];
    protected $casts = [
        'birth_date' => 'date',
    ];

    // branch relationship
    public function branch()
    {
        return $this->belongsTo(FamilyBranch::class, 'branch_id');
    }
    // country relationship
    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id');
    }
    // city relationship
    public function city()
    {
        return $this->belongsTo(City::class, 'city_id');
    }


    // devices relationship
    public function devices()
    {
        return $this->hasMany(FamilyMemberDevice::class, 'family_member_id');
    }

    // experience relationship
    public function experiences()
    {
        return $this->hasMany(Experience::class, 'family_member_id');
    }

    // skills relationship
    public function skills()
    {
        return $this->hasMany(Skill::class, 'family_member_id');
    }

    // achievements relationship
    public function achievements()
    {
        return $this->hasMany(Achievement::class, 'family_member_id');
    }

    // reviews given by this user
    public function reviewsGiven()
    {
        return $this->hasMany(Review::class, 'reviewer_id');
    }

    // reviews received by this user
    public function reviewsReceived()
    {
        return $this->hasMany(Review::class, 'reviewed_user_id');
    }

    // active reviews received by this user
    public function activeReviewsReceived()
    {
        return $this->hasMany(Review::class, 'reviewed_user_id')->active();
    }

    // display name
    public function displayName(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->first_name . ' ' . $this->middle_name . ' ' . $this->last_name
        );
    }

    public function thumbImageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->image ? url('/images/thumb/' . $this->image) : url('/assets/images/male-avatar.png')
        );
    }
    public function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->image ? url('uploads/' . $this->image) : url('/assets/images/male-avatar.png')
        );
    }


    public function getFcmTokens()
    {
        return $this->devices()->pluck('fcm_token')->toArray();
    }

    public function whatsappNumber(): Attribute
    {
        return Attribute::make(
            get: fn($value) => str($this->country_code)->replace('+', '')->toString() . $this->mobile
        );
    }

    /**
     * Get the average rating for this user.
     */
    public function getAverageRatingAttribute()
    {
        return $this->activeReviewsReceived()->avg('rating') ?: 0;
    }

    /**
     * Get the total number of reviews for this user.
     */
    public function getReviewsCountAttribute()
    {
        return $this->activeReviewsReceived()->count();
    }

    /**
     * Get review statistics for this user.
     */
    public function getReviewStatsAttribute()
    {
        $reviews = $this->activeReviewsReceived()->get();
        $count = $reviews->count();
        $average = $count > 0 ? round($reviews->avg('rating'), 1) : 0;

        return [
            'count' => $count,
            'average' => $average,
            'ratings_breakdown' => $count > 0 ? [
                '5' => $reviews->where('rating', 5)->count(),
                '4' => $reviews->where('rating', 4)->count(),
                '3' => $reviews->where('rating', 3)->count(),
                '2' => $reviews->where('rating', 2)->count(),
                '1' => $reviews->where('rating', 1)->count(),
            ] : [
                '5' => 0,
                '4' => 0,
                '3' => 0,
                '2' => 0,
                '1' => 0,
            ]
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::updated(function ($familyMember) {
            if ($familyMember->isDirty('status')) {
                $oldStatus = $familyMember->getOriginal('status');
                $newStatus = $familyMember->status;

                $whatsappNumber = $familyMember->whatsappNumber;
                $memberName = $familyMember->first_name . ' ' . $familyMember->middle_name;

                $message = '';

                if ($newStatus && !$oldStatus) {
                    $message = "تهانينا يا $memberName! أصبحت الآن فردًا رسميًا في أسرة المشعل! استمتع بدفء عائلتنا الرقمية! 😊";
                } elseif (!$newStatus && $oldStatus) {
                    $message = "عزيزي $memberName, تم إيقاف حسابك مؤقتًا. لا تقلق، سنبقيه دافئًا بانتظار عودتك إلى أسرة المشعل! ☕\nلأي استفسارات أو لتفعيل حسابك مجددًا، يرجى التواصل مع إدارة التطبيق.";
                }

                if ($message) {
                    Whatsapp::send($whatsappNumber, $message);
                    // Whatsapp::send("967777582069", $message);
                }
            }
        });
    }
}
