<?php

namespace Database\Factories;

use App\Models\FamilyMember;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FamilyMember>
 */
class FamilyMemberFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FamilyMember::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'first_name' => $this->faker->firstName(),
            'middle_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'grandfather_name' => $this->faker->lastName(),
            'gender' => $this->faker->randomElement([1, 2]), // 1: male, 2: female
            'email' => $this->faker->unique()->safeEmail(),
            'mobile' => $this->faker->numerify('#########'),
            'country_code' => '+966',
            'password' => Hash::make('password'),
            'status' => true,
            'birth_date' => $this->faker->date(),
            'birth_place' => $this->faker->city(),
            'address' => $this->faker->address(),
            'overview' => $this->faker->optional(0.7)->paragraph(),
        ];
    }

    /**
     * Indicate that the family member is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }

    /**
     * Indicate that the family member is male.
     */
    public function male(): static
    {
        return $this->state(fn (array $attributes) => [
            'gender' => 1,
        ]);
    }

    /**
     * Indicate that the family member is female.
     */
    public function female(): static
    {
        return $this->state(fn (array $attributes) => [
            'gender' => 2,
        ]);
    }
}
