<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('reviewer_id')->constrained('family_members')->onDelete('cascade');
            $table->foreignId('reviewed_user_id')->constrained('family_members')->onDelete('cascade');
            $table->tinyInteger('rating')->unsigned()->comment('Rating from 1 to 5');
            $table->text('comment')->nullable();
            $table->boolean('status')->default(1)->comment('1: active, 0: inactive');
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['reviewed_user_id', 'status']);
            $table->index(['reviewer_id', 'status']);
            $table->index('rating');

            // Unique constraint to prevent duplicate reviews
            $table->unique(['reviewer_id', 'reviewed_user_id'], 'unique_reviewer_reviewed');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
