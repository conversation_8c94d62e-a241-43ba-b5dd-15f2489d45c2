<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('v1')
    ->middleware(['apiKey'])
    ->group(function () {
        Route::post('/login', [App\Http\Controllers\Api\AuthController::class, 'login']);
        Route::post('/register', [App\Http\Controllers\Api\AuthController::class, 'register']);
        Route::post('/forget-password', [App\Http\Controllers\Api\AuthController::class, 'forgetPassword']);
        Route::post('/reset-password', [App\Http\Controllers\Api\AuthController::class, 'resetPassword']);
        Route::post('/device/register', [App\Http\Controllers\Api\AuthController::class, 'registerDevice']);
        Route::post('/check-registration-status', [App\Http\Controllers\Api\AuthController::class, 'checkRegistrationStatus']);
        Route::get('/branches', [App\Http\Controllers\Api\GeneralController::class, 'getFamilyBranches']);
        Route::get('/social-media-links', [App\Http\Controllers\Api\SocialMediaLinkController::class, 'index']);
        Route::get('/countries', [App\Http\Controllers\Api\GeneralController::class, 'getCountries']);
        Route::get('/cities/{id}', [App\Http\Controllers\Api\GeneralController::class, 'getCities']);
        Route::get('/news', [App\Http\Controllers\Api\NewsController::class, 'index']);
        Route::get('/news/search', [App\Http\Controllers\Api\NewsController::class, 'search']);
        Route::get('/news/{id}', [App\Http\Controllers\Api\NewsController::class, 'show']);
        Route::post('/contact', [App\Http\Controllers\Api\MessageController::class, 'create']);
        Route::get('/albums', [App\Http\Controllers\Api\AlbumController::class, 'index']);
        Route::get('/album/{id}', [App\Http\Controllers\Api\AlbumController::class, 'show']);
        Route::get('/albums/search', [App\Http\Controllers\Api\AlbumController::class, 'search']);
        Route::get('/occasions', [App\Http\Controllers\Api\OccasionController::class, 'index']);
        Route::get('/occasions/search', [App\Http\Controllers\Api\OccasionController::class, 'search']);
        Route::get('/occasions/count', [App\Http\Controllers\Api\OccasionController::class, 'count']);
        Route::get('/occasion/{id}', [App\Http\Controllers\Api\OccasionController::class, 'show']);
        Route::get('/excellence-awards', [App\Http\Controllers\Api\ExcellenceAwardController::class, 'index']);
        Route::get('/excellence-awards/search', [App\Http\Controllers\Api\ExcellenceAwardController::class, 'search']);
        Route::get('/excellence-award/{id}', [App\Http\Controllers\Api\ExcellenceAwardController::class, 'show']);
        Route::get('/committees', [App\Http\Controllers\Api\CommitteeController::class, 'index']);
        Route::get('/family-information/tree-image', [App\Http\Controllers\Api\FamilyInformationController::class, 'treeImage']);
        Route::get('/tree/nodes', [App\Http\Controllers\Api\FamilyTreeNodeController::class, 'index']);
        Route::get('/family-information/about', [App\Http\Controllers\Api\FamilyInformationController::class, 'about']);
        Route::get('/slide-data', [App\Http\Controllers\Api\FamilyInformationController::class, 'slideData']);
        Route::get('/comments/{id}/{type}', [App\Http\Controllers\Api\CommentController::class, 'index']);
        Route::post('/record-visit', [App\Http\Controllers\Api\VisitController::class, 'recordVisit']);

        Route::get('/profile', [App\Http\Controllers\Api\ProfileController::class, 'profile']);
        Route::get('/profile/experiences', [App\Http\Controllers\Api\ProfileController::class, 'getExperiences']);
        Route::get('/profile/skills', [App\Http\Controllers\Api\ProfileController::class, 'getSkills']);
        Route::get('/profile/achievements', [App\Http\Controllers\Api\ProfileController::class, 'getAchievements']);


        Route::middleware(['auth:sanctum'])
            ->group(function () {
                Route::post('/logout', [App\Http\Controllers\Api\AuthController::class, 'logout']);
                Route::post('/change-password', [App\Http\Controllers\Api\AuthController::class, 'changePassword']);
                Route::post('/update-profile', [App\Http\Controllers\Api\ProfileController::class, 'updateProfile']);
                Route::post('/update-profile-image', [App\Http\Controllers\Api\ProfileController::class, 'updateProfileImage']);
                Route::post('/update-profile-cover', [App\Http\Controllers\Api\ProfileController::class, 'updateProfileCover']);
                Route::post('/cv/update', [App\Http\Controllers\Api\ProfileController::class, 'updateCV']);
                Route::post('/comment', [App\Http\Controllers\Api\CommentController::class, 'store']);
                Route::post('/comment/{id}/delete', [App\Http\Controllers\Api\CommentController::class, 'destroy']);
                Route::post('/comment/{id}/like', [App\Http\Controllers\Api\CommentController::class, 'like']);
                Route::post('/comment/{id}/unlike', [App\Http\Controllers\Api\CommentController::class, 'unlike']);
                Route::post('/tree/nodes/request', [App\Http\Controllers\Api\FamilyTreeRequestController::class, 'store']);
                Route::post('/profile/experience/create', [App\Http\Controllers\Api\ProfileController::class, 'createExperience']);
                Route::post('/profile/experience/{id}/update', [App\Http\Controllers\Api\ProfileController::class, 'updateExperience']);
                Route::delete('/profile/experience/{id}/delete', [App\Http\Controllers\Api\ProfileController::class, 'deleteExperience']);
                Route::post('/profile/skill/create', [App\Http\Controllers\Api\ProfileController::class, 'createSkill']);
                Route::post('/profile/skill/{id}/update', [App\Http\Controllers\Api\ProfileController::class, 'updateSkill']);
                Route::delete('/profile/skill/{id}/delete', [App\Http\Controllers\Api\ProfileController::class, 'deleteSkill']);
                Route::post('/profile/achievement/create', [App\Http\Controllers\Api\ProfileController::class, 'createAchievement']);
                Route::post('/profile/achievement/{id}/update', [App\Http\Controllers\Api\ProfileController::class, 'updateAchievement']);
                Route::delete('/profile/achievement/{id}/delete', [App\Http\Controllers\Api\ProfileController::class, 'deleteAchievement']);

                // Review routes
                Route::post('/reviews', [App\Http\Controllers\Api\ReviewController::class, 'store']);
                Route::get('/users/{id}/reviews', [App\Http\Controllers\Api\ReviewController::class, 'index']);
                Route::get('/users/{id}/reviews/summary', [App\Http\Controllers\Api\ReviewController::class, 'summary']);
                Route::put('/reviews/{id}', [App\Http\Controllers\Api\ReviewController::class, 'update']);
                Route::patch('/reviews/{id}', [App\Http\Controllers\Api\ReviewController::class, 'update']);
                Route::delete('/reviews/{id}', [App\Http\Controllers\Api\ReviewController::class, 'destroy']);
            });
    });
Route::prefix('v2')
    ->middleware(['apiKey'])
    ->group(function () {
        Route::get('/albums', [App\Http\Controllers\Api\AlbumController::class, 'index']);
        Route::get('/album/{id}', [App\Http\Controllers\Api\AlbumController::class, 'show']);
    });
